// components/ImageModal.tsx
import { useState } from "react";
import { X } from "lucide-react"; // Assuming you're using lucide-react for icons

interface ImageModalProps {
  isOpen: boolean;
  imageUrl: string;
  onClose: () => void;
}

export const ImageModal = ({ isOpen, imageUrl, onClose }: ImageModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
      <div className="relative max-w-4xl w-full">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-white bg-gray-800 rounded-full p-2 hover:bg-gray-600"
        >
          <X size={24} />
        </button>
        <img
          src={imageUrl}
          alt="Proof Image"
          className="w-full h-auto max-h-[80vh] object-contain"
        />
      </div>
    </div>
  );
};