import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import fourcirclegif from "@/assets/icons/circlesfour.gif";
import { Button } from "@/components/ui/button";
import { useNavigate, useLocation} from "react-router-dom";
import { useEffect, useState } from "react";
import { customAxios } from "@/utils/axio-interceptor";
// import { useDispatch } from "react-redux";
// import { logout } from "@/store/slices/authSlice";
import { Loader2 } from "lucide-react";
import { useAppSelector } from "@/store/hooks/reduxHooks";

const NewBlankDashboard = () => {
  const navigate = useNavigate();
//   const dispatch = useDispatch();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const userRole = useAppSelector((state) => state.auth.user?.role);
  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);

  // Prevent direct URL access
  useEffect(() => {
    if (!location.pathname.endsWith("blank-page-after-payment-successful")) {
      navigate("/dashboard/blank-page-after-payment-successful", {
        replace: true,
      });
    }
  }, [location.pathname, navigate]);

  // Handle back button and URL changes
  useEffect(() => {
    const handleUrlChange = () => {
      if (
        !window.location.pathname.endsWith(
          "blank-page-after-payment-successful"
        )
      ) {
        window.history.pushState(
          null,
          "",
          "/blank-page-after-payment-successful"
        );
      }
    };

    window.addEventListener("popstate", handleUrlChange);
    return () => window.removeEventListener("popstate", handleUrlChange);
  }, []);

  // Check annotator count
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/auth/login", { replace: true });
      return;
    }

    const checkAnnotatorCount = async () => {
      try {
        setIsLoading(true);
        if (userRole === "CLIENT") {
          const response = await customAxios.get(
            "/v1/dashboard/client-annotators"
          );
          if (response.data?.count > 0) {
            navigate("/dashboard", { replace: true });
          }
        } else if (userRole === "COWORKER") {
          navigate("/dashboard", { replace: true });
        }
      } catch (error) {
        console.error("Error checking annotator count:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAnnotatorCount();
  }, [navigate, userRole, isAuthenticated]);

  // const handleContinue = useCallback(() => {
  //   navigate("/dashboard");
  // }, [navigate]);

 
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="flex flex-col gap-4 items-center justify-center flex-grow p-4">
        <img
          src={fourcirclegif}
          alt="Payment successful"
          className="w-[15rem] h-[15rem]"
        />

        <div className="flex flex-row justify-center items-center gap-[0.5rem] text-center">
          <IoMdCheckmarkCircleOutline className="w-[35px] h-[35px] text-[#20BF55]" />
          <h1 className="text-[40px] font-bold">Please check your Mail</h1>
        </div>

        <div className="flex flex-row justify-center gap-4">
         
          <Button
            variant={"ghost"}
            className="border px-[18px] text-[18px] border-[#E91C24] text-[#E91C24]"
           
          >
            Continue to Website
          </Button>
          <Button
            variant={"gradient"}
            className="px-[40px] text-[18px]"
            
          >
            Contact Us
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NewBlankDashboard;
