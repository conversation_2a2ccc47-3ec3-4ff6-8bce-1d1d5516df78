"use client";

import { customAxios } from "@/utils/axio-interceptor";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, FormProvider } from "react-hook-form";
import "react-toastify/dist/ReactToastify.css";
import { z } from "zod";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { FormField } from "@/components/ui/form";
import { AuthCommonComponent } from "./common/AuthCommon";
import OtpInput from "./common/OtpInput";
import {
  ClientVerifyOtp,
  ClientSignupOtp,
  ClientResendOtp,
  ResetVerifyOtp,
} from "../api/client-api";
import { toast } from "react-toastify";
import { redirectToDashboard } from "@/utils/navigaterole";
import { useDispatch } from "react-redux";
import { setUser } from "@/store/slices/authSlice";
import { setUserProfile } from "@/store/slices/userSlice";
import img1 from "@/assets/darklogo.png";
import { useResponsive } from "@/hooks/use-responsive";
import { CustomToast } from "@/_components/common";
import { AnnonatorblankPageApi } from "../api/annoatorblanpage";

// ✅ Zod schema
const FormSchema = z.object({
  otp: z.string().min(4, {
    message: "Your one-time password must be 4 characters.",
  }),
  email: z.string().email({
    message: "Invalid email address.",
  }),
});

export function InputOTPForm() {
  const [countdown, setCountdown] = useState(30);
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLaptopMd, isLaptopLg } = useResponsive();
  const [isResending, setIsResending] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      otp: "",
      email: location.state?.email || "",
    },
  });

  // ✅ Check annotator count and redirect accordingly
  const checkAnnotatorCountAndRedirect = async (user: any) => {
    try {
      if (user.role === "CLIENT") {
        const annotatorData = await AnnonatorblankPageApi();
        if (annotatorData.count === 0) {
          navigate("/dashboard/blank-page-after-payment-successful", { replace: true });
          return;
        }
      }
      // Default redirect for other cases
      redirectToDashboard(user.role, navigate);
    } catch (error) {
      console.error("Error checking annotator count:", error);
      // Fallback to default redirect if there's an error
      redirectToDashboard(user.role, navigate);
    }
  };

  // ✅ Verify OTP
  const handleVerify = async () => {
    const { email, otp } = form.getValues();
    const fromSignup = location.state?.fromSignup || false;
    const fromReset = location.state?.fromReset || false;

    if (!email || !otp) {
      toast.error("OTP and email are required.");
      return;
    }

    if (otp.length !== 4 || !/^\d+$/.test(otp)) {
      toast.error("Please enter a valid 4-digit OTP.");
      return;
    }

    setIsLoading(true);
    const toastId = toast.loading(
      fromSignup ? "Verifying your email..." : "Verifying OTP...",
      {
        position: "top-right",
        autoClose: false,
      }
    );

    try {
      let response;
      if (fromSignup) {
        response = await ClientSignupOtp(email, otp);
      } else if (fromReset) {
        response = await ResetVerifyOtp(email, otp);
      } else {
        response = await ClientVerifyOtp(email, otp);
      }

      toast.update(toastId, {
        render: fromSignup
          ? "Email verified successfully!"
          : "OTP verified successfully!",
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });

      const { user: apiUser, token } = response;

      // Update Redux store with user data
      dispatch(setUser({ user: apiUser, token }));
      dispatch(
        setUserProfile({
          name: apiUser.name,
          email: apiUser.email,
          role: apiUser.role,
          permissions: apiUser.permissions,
        })
      );

      // Fetch complete user data including plan and bank-transfer status
      const userResponse = await customAxios.get("/v1/clients/me", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const { user: fetchedUser } = userResponse.data;

      // Navigation logic based on user status
      setTimeout(() => {
        if (fromReset) {
          // Password reset flow
          navigate("/auth/reset-password", {
            state: { email, token },
            replace: true,
          });
        } else if (!fetchedUser.plan) {
          // User needs to complete profile and purchase plan
          navigate("/auth/profile/authprofile", {
            state: {
              email: email,
              fromSignup: fromSignup,
            },
            replace: true,
          });
        } else if (fetchedUser.plan && !fetchedUser["bank-transfer"]) {
          // Plan purchased but bank transfer not completed
          navigate("/dashboard/blank-page-after-payment-successful", {
            replace: true,
          });
        } else if (fetchedUser.plan && fetchedUser["bank-transfer"]) {
          // Both plan and bank transfer completed
          navigate("/bantransfer-blank-page", {
            replace: true,
          });
        } else {
          // Default case - check annotator count
          checkAnnotatorCountAndRedirect(apiUser);
        }
      }, 0);
    } catch (error: any) {
      console.error("Error during OTP verification or user fetch:", error);

      let errorMessage = "Invalid OTP. Please check the code and try again.";
      if (error.response?.status === 400) {
        errorMessage =
          error.response.data.message ||
          "Invalid OTP. Please check the code and try again.";
      } else if (error.response?.status === 401) {
        errorMessage = "Unauthorized request. Please log in again.";
      } else {
        errorMessage = error.message || "An error occurred. Please try again.";
      }

      toast.update(toastId, {
        render: errorMessage,
        type: "error",
        isLoading: false,
        autoClose: 4000,
      });

      form.setError("otp", {
        type: "manual",
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // ✅ Resend OTP
  const handleResendOtp = async () => {
    const { email, otp } = form.getValues();
    const fromReset = location.state?.fromReset || false;

    if (countdown > 0) return;

    setIsResending(true);
    try {
      if (fromReset) {
        // For password reset flow, we need both email and otp
        await ResetVerifyOtp(email, otp);
      } else {
        // For regular OTP resend, only email is needed
        await ClientResendOtp(email);
      }

      setToastMessage("OTP resent successfully!");
      setShowToast(true);
      setCountdown(30);
    } catch (error) {
      console.error("Failed to resend OTP:", error);
      setToastMessage("Failed to resend OTP. Please try again.");
      setShowToast(true);
    } finally {
      setIsResending(false);
    }
  };

  // ✅ Timer countdown
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // Responsive styling based on screen size
  const getStyles = () => {
    if (isLaptopLg) {
      return {
        container:
          "w-full h-full flex flex-row justify-center gap-x-6 shadow-[0px_4px_48px_10px_#0000000F]",
        leftColumn: "flex flex-col w-[48%]",
        logoContainer: "flex justify-start items-start p-8",
        logoSize: "w-52",
        rightColumn: "w-[48%] flex justify-end items-end",
        resendText: "text-base text-gray-600",
        resendButton: "text-[#FF577F] font-semibold hover:underline text-base",
        timerText: "text-base text-gray-500",
      };
    } else if (isLaptopMd) {
      return {
        container:
          "w-full h-full flex flex-row justify-center gap-x-4 shadow-[0px_4px_48px_10px_#0000000F]",
        leftColumn: "flex flex-col w-[49%]",
        logoContainer: "flex justify-start items-start p-7",
        logoSize: "w-48",
        rightColumn: "w-[47%] flex justify-end items-end",
        resendText: "text-sm text-gray-600",
        resendButton: "text-[#FF577F] font-semibold hover:underline text-sm",
        timerText: "text-sm text-gray-500",
      };
    } else {
      return {
        container:
          "w-full h-full flex flex-row justify-center gap-x-2 shadow-[0px_4px_48px_10px_#0000000F]",
        leftColumn: "flex flex-col w-[50%]",
        logoContainer: "flex justify-start items-start p-6",
        logoSize: "w-44",
        rightColumn: "w-[45%] flex justify-end items-end",
        resendText: "text-sm text-gray-600",
        resendButton: "text-[#FF577F] font-semibold hover:underline text-sm",
        timerText: "text-sm text-gray-500",
      };
    }
  };

  const styles = getStyles();

  return (
    <div className={styles.container}>
      <div className={styles.leftColumn}>
        <div className={styles.logoContainer}>
          <img src={img1} alt="logoimage" className={styles.logoSize} />
        </div>

        <div className="flex flex-col h-full justify-center items-center">
          <FormProvider {...form}>
            <div className="flex flex-col items-center gap-4">
              <FormField
                control={form.control}
                name="otp"
                render={({ field, fieldState }) => (
                  <div
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleVerify();
                      }
                    }}
                  >
                    <OtpInput
                      value={field.value}
                      onChange={field.onChange}
                      error={fieldState.error?.message}
                      onVerify={handleVerify}
                      isSubmitting={isLoading}
                      screenSize={
                        isLaptopLg ? "large" : isLaptopMd ? "medium" : "small"
                      }
                    />
                  </div>
                )}
              />

              <div className="flex flex-col items-start justify-start mt-4 gap-2">
                <div className="flex flex-row items-center gap-2">
                  <p className={styles.resendText}>Didn't receive the code?</p>
                  {countdown > 0 ? (
                    <span className={styles.timerText}>Resend OTP in {countdown}s</span>
                  ) : (
                    <button
                      className={styles.resendButton}
                      onClick={handleResendOtp}
                      disabled={isResending}
                    >
                      {isResending ? "Sending..." : "Resend OTP"}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </FormProvider>
        </div>

        {showToast && (
          <div className="fixed bottom-4 right-4 z-50">
            <CustomToast
              title="OTP Verification"
              message={toastMessage}
              onClose={() => setShowToast(false)}
            />
          </div>
        )}
      </div>

      <div className={styles.rightColumn}>
        <AuthCommonComponent />
      </div>
    </div>
  );
}

export default function OtpPage() {
  const { isLaptopMd, isLaptopLg } = useResponsive();

  const getContainerClass = () => {
    if (isLaptopLg) return "w-full h-screen otp-page-large";
    if (isLaptopMd) return "w-full h-screen otp-page-medium";
    return "w-full h-screen otp-page-small";
  };

  return (
    <div className={getContainerClass()}>
      <InputOTPForm />
    </div>
  );
}