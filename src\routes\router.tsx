import {
  Navigate,
  Route,
  createBrowserRouter,
  createRoutesFromElements,
} from "react-router-dom";
import { USER_ROLES } from "@/utils/constants";

import AuthRoutes from "@/features/auth/routes";
import { AuthCoworker } from "@/features/Coworker/auth/authcoworker";

import DashboardSidebar from "@/features/layout/dashboard/newdahboard-layout";
import { ClientDashboardPage } from "@/features/clientdashboard";
import AnnotatorDashboard from "@/features/annotator/dashboard/annotator";
import AnnotatorProjectManagement from "@/features/annotator/dashboard/projectmangement";
import AnnotatorDetails from "@/features/annotator/annotatordescription";
import FAQs from "@/features/settings/faqs";
import SettingFile from "@/features/settings/setting-file";
import Chat from "@/features/chat/routes/chat";
import TaskDetails from "@/features/clientdashboard/task-detail-page/components/taskdetails";
import ProjectDetails from "@/features/clientdashboard/task-detail-page/components/projectdetails/projectdetails";
import Attendacelog from "@/features/clientdashboard/annoatorattendancehistory/attendance.log.tsx";
import AddOn from "@/features/clientdashboard/add-on/component/addon";
import Coworkers from "@/features/clientdashboard/task-detail-page/components/coworker";
import AdminFAQs from "@/features/admindashboard/components/adminfaqs";
import AdminTaskDetails from "@/features/admindashboard/detailsadmin/AdminTaskDetails";
import AdminCoodinator from "@/features/admindashboard/detailsadmin/coodinatoradmin/components/attendance.log";
import AdminClients from "@/features/admindashboard/detailsadmin/clientadmin/components/attendance.log";
import AdminOnboard from "@/features/admindashboard/onboard/attendance.log";
import MatchMakingmain from "@/features/admindashboard/matchmaking";
import UserAllList from "@/features/admindashboard/userall";
import AdminsList from "@/features/admindashboard/admin-list";
import AdminDashboard from "@/features/admindashboard/components/admindashboard";
import Clientsadmin from "@/features/admindashboard/detailsadmin/clientadmin";
import Annotators from "@/features/admindashboard/detailsadmin/annonatoradmin";
import Projects from "@/features/admindashboard/detailsadmin/projectadmin";
import AttendanceAnnotator from "@/features/annotator/dashboard/attendence";
import ProjectCordinator from "@/features/projectcordinator/dashboard/projectCoardinator";
import CordTaskDetails from "@/features/projectcordinator/cordinayordetailscard/AdminTaskDetails";
import Clientscord from "@/features/projectcordinator/cordinayordetailscard/clientadmin";
import CordAnnotators from "@/features/projectcordinator/cordinayordetailscard/annonatorcoordinator";
import CoordirProjects from "@/features/projectcordinator/cordinayordetailscard/projectadmin";
import ClientAnnotators from "@/features/clientdashboard/task-detail-page/components/annonator";
import ClientProjects from "@/features/clientdashboard/task-detail-page/components/project";
import PackageAdmin from "@/features/admindashboard/packageadmin";
import UserDetails from "@/features/admindashboard/userall/components/formuserdetails/userdetails";
import InviteCoworker from "@/features/clientdashboard/invitecoworker";
import AnnotatorProjects from "@/features/clientdashboard/task-detail-page/annotatorprojects";
import AdminProjectDetails from "@/features/admindashboard/detailsadmin/adminprojectdesc";
import CoordinatorProjectDetails from "@/features/projectcordinator/coordinatordetails";
import CoordinatorProjects from "@/features/projectcordinator/cordinayordetailscard/clientadmin/coordinatorprojects";
import CoordinatorAnnotatorProjects from "@/features/projectcordinator/annotatorprojects";
import CoodinatorAdmin from "@/features/admindashboard/detailsadmin/coodinatoradmin";
import CordinatorNotification from "@/features/projectcordinator/notification/notification";
import AdminNotification from "@/features/admindashboard/adminnotification";
import ClientNotification from "@/features/clientdashboard/clientnotification/index";
import AnnonatorNotification from "@/features/annotator/dashboard/annonator_notification/annonator_notification";
import CoordinatorClientProjects from "@/features/projectcordinator/clientprojects";
import BankOtherTableRoute from "@/features/admindashboard/bankothertable";
import BillingRoute from "@/features/clientdashboard/billing";
import Subscription from "@/features/clientdashboard/billing/subscription/index";
import FormProfileChoose from "@/features/auth/formprofilechoose";
import ClientProfileRoute from "@/features/layout/clientprofile";
import SignupClientQuestionform from "@/features/auth/formprofilechoose/signupclientquestionform/signupclientquestionform";
import AdminClientProfileRoute from "@/features/admindashboard/detailsadmin/clientadmin/components/adminclientprofile/adminclientprofile";
import AddOnQuestioniare from "@/features/clientdashboard/add-on/component/commonpackageplan/addquestioniare";
import BlankDashboard from "@/features/clientdashboard/add-on/component/blankdashboard";
import PaymentMethod from "@/features/clientdashboard/billing/payment_method/paymentmethod";
import RoleProtectedRoute from "./private.route";
import PaymentSuccess from "@/features/clientdashboard/payment-success";
import PaymentFailed from "@/features/clientdashboard/payment-failed";
import NewBlankDashboard from "@/features/clientdashboard/add-on/component/commonpackageplan/banktranferblankdashboard";
import SupportList from "@/features/clientdashboard/support/add-support-page";
import OnboardPasswordLink from "@/features/admindashboard/onboard/components/annonaotrcordiantorpasword";
import AddonBillingForm from "@/features/clientdashboard/add-on/component/commonpackageplan/addonaddress.checkout";

const RootRedirect = () => {
  const token = localStorage.getItem("access_token");
  const role = localStorage.getItem("role");

  if (!token) return <Navigate to="/auth/login" />;

  switch (role) {
    case USER_ROLES.CLIENT:
    case USER_ROLES.COWORKER:
      return <Navigate to="/dashboard" />;
    case USER_ROLES.ANNOTATOR:
      return <Navigate to="/annotator/list" />;
    case USER_ROLES.PROJECT_COORDINATOR:
      return <Navigate to="/coordinator/dashboardlist" />;
    case USER_ROLES.ADMIN:
      return <Navigate to="/admin/dashboard" />;
    default:
      return <Navigate to="/auth/login" />;
  }
};

export const router = createBrowserRouter(
  createRoutesFromElements(
    <Route>
      {/* Root Redirect */}
      <Route path="/*" element={<RootRedirect />} />
      <Route path="/auth/*" element={<AuthRoutes />} />

      {/* Public Routes */}
      {/* <Route
        path="/blank-page-after-payment-successful"
        element={<BlankDashboard />}
      /> */}
      <Route path="/coworker/accept-invite" element={<AuthCoworker />} />
      <Route path="/profile/authprofile" element={<FormProfileChoose />} />
      <Route path="/plan-select" element={<SignupClientQuestionform />} />

      <Route path="/paypal/success" element={<PaymentSuccess />} />
      <Route path="/paypal/failed" element={<PaymentFailed />} />
      <Route
        path="/bantransfer-blank-page"
        element={<NewBlankDashboard />}
      />
      <Route path="/passwordLink" element={<OnboardPasswordLink />} />

      {/* Client/Coworker Routes */}
      <Route
        element={
          <RoleProtectedRoute
            allowedRoles={[USER_ROLES.CLIENT, USER_ROLES.COWORKER]}
            skipAnnotatorCheck={true}
          />
        }
      >
        <Route element={<DashboardSidebar />}>
          <Route
            path="/dashboard/plan-select"
            element={<SignupClientQuestionform />}
          />
          <Route
            path="/dashboard/blank-page-after-payment-successful"
            element={<BlankDashboard />}
          />
          <Route
            path="/dashboard/user-profile-data/:id"
            element={<ClientProfileRoute />}
          />
          <Route path="/dashboard" element={<ClientDashboardPage />} />
          <Route path="/dashboard/chat" element={<Chat />} />
          <Route
            path="/dashboard/notification"
            element={<ClientNotification />}
          />
          <Route
            path="/dashboard/invite-coworker"
            element={<InviteCoworker />}
          />

          <Route path="/dashboard/task-details" element={<TaskDetails />}>
            <Route index element={<Navigate to="projects" replace />} />
            <Route path="projects" element={<ClientProjects />} />
            <Route path="annotators" element={<ClientAnnotators />} />
          </Route>

          <Route
            path="/dashboard/project-details"
            element={<ProjectDetails />}
          />
          <Route
            path="/dashboard/annotatorproject"
            element={<AnnotatorProjects />}
          />
          <Route path="/dashboard/attendance" element={<Attendacelog />} />
          <Route path="/dashboard/addon" element={<AddOn />} />
          <Route
            path="/dashboard/addon-questinaire"
            element={<AddOnQuestioniare />}
          />
          <Route path="/dashboard/address-billing" element={<AddonBillingForm />} />
          <Route path="/dashboard/billing" element={<BillingRoute />} />
          <Route
            path="/dashboard/billing/subscription"
            element={<Subscription />}
          />
          <Route
            path="/dashboard/billing/payment-method"
            element={<PaymentMethod />}
          />
          <Route path="/dashboard/faq" element={<FAQs />} />
          <Route path="/dashboard/settings" element={<SettingFile />} />
          <Route path="/dashboard/coworker" element={<Coworkers />} />
          <Route
            path="/dashboard/paypal/success"
            element={<PaymentSuccess />}
          />
          <Route path="/dashboard/paypal/failed" element={<PaymentFailed />} />
          <Route path="/dashboard/support" element={<SupportList />} />
        </Route>
      </Route>

      {/* Admin Routes */}
      <Route element={<RoleProtectedRoute allowedRoles={[USER_ROLES.ADMIN]} />}>
        <Route element={<DashboardSidebar />}>
          <Route path="/admin/dashboard" element={<AdminDashboard />} />
          <Route path="/admin/chat" element={<Chat />} />
          <Route path="/admin/adminsettings" element={<SettingFile />} />
          <Route path="/admin/faqsadmin" element={<AdminFAQs />} />

          <Route path="/admin/admindetails" element={<AdminTaskDetails />}>
            <Route index element={<Navigate to="clients" replace />} />
            <Route path="clients" element={<Clientsadmin />} />
            <Route path="annotators" element={<Annotators />} />
            <Route path="coordinators" element={<CoodinatorAdmin />} />
            <Route path="projects" element={<Projects />} />
          </Route>

          <Route path="/admin/admincoodinator" element={<AdminCoodinator />} />
          <Route path="/admin/adminclients" element={<AdminClients />} />
          <Route path="/admin/onboard" element={<AdminOnboard />} />
          <Route path="/admin/notifications" element={<AdminNotification />} />
          <Route path="/admin/match-making" element={<MatchMakingmain />} />
          <Route path="/admin/total-users" element={<UserAllList />} />
          <Route
            path="/admin/confirm-others-payment"
            element={<BankOtherTableRoute />}
          />
          <Route
            path="/admin/total-users/user-details/:id"
            element={<UserDetails />}
          />
          <Route
            path="/admin/admindetails/clients/:clientId"
            element={<AdminClientProfileRoute />}
          />
          <Route path="/admin/admins-list" element={<AdminsList />} />
          <Route path="/admin/package-admin" element={<PackageAdmin />} />
          <Route
            path="/admin/adminproject-details"
            element={<AdminProjectDetails />}
          />
          <Route path="/admin/attendance" element={<Attendacelog />} />
          <Route
            path="/admin/user-profile-data/:id"
            element={<ClientProfileRoute />}
          />
        </Route>
      </Route>

      {/* Annotator Routes */}
      <Route
        element={<RoleProtectedRoute allowedRoles={[USER_ROLES.ANNOTATOR]} />}
      >
        <Route element={<DashboardSidebar />}>
          <Route path="/annotator/list" element={<AnnotatorDashboard />} />
          <Route
            path="/annonator/notification"
            element={<AnnonatorNotification />}
          />
          <Route
            path="/annotator/annotatordetail"
            element={<AnnotatorProjectManagement />}
          />
          <Route
            path="/annotator/user-profile-data/:id"
            element={<ClientProfileRoute />}
          />
          <Route
            path="/annotator/annotatordescription"
            element={<AnnotatorDetails />}
          />
          <Route path="/annotator/chat" element={<Chat />} />
          <Route
            path="/annotator/attendance"
            element={<AttendanceAnnotator />}
          />
          <Route path="/annotator/faq" element={<FAQs />} />
          <Route path="/annotator/settings" element={<SettingFile />} />
        </Route>
      </Route>

      {/* Project Coordinator Routes */}
      <Route
        element={
          <RoleProtectedRoute allowedRoles={[USER_ROLES.PROJECT_COORDINATOR]} />
        }
      >
        <Route element={<DashboardSidebar />}>
          <Route
            path="/coordinator/dashboardlist"
            element={<ProjectCordinator />}
          />
          <Route
            path="/coordinator/user-profile-data/:id"
            element={<ClientProfileRoute />}
          />

          <Route
            path="/coordinator/projectdetails"
            element={<CordTaskDetails />}
          >
            <Route index element={<Navigate to="clients" replace />} />
            <Route path="clients" element={<Clientscord />} />
            <Route path="annotators" element={<CordAnnotators />} />
            <Route path="projects" element={<CoordirProjects />} />
          </Route>

          <Route path="/coordinator/chat" element={<Chat />} />
          <Route
            path="/coordinator/notification"
            element={<CordinatorNotification />}
          />
          <Route path="/coordinator/faq" element={<FAQs />} />
          <Route path="/coordinator/settings" element={<SettingFile />} />
          <Route
            path="/coordinator/coordinatorproject-details"
            element={<CoordinatorProjectDetails />}
          />
          <Route path="/coordinator/attendance" element={<Attendacelog />} />
          <Route
            path="/coordinator/coordinatorprojects"
            element={<CoordinatorProjects />}
          />
          <Route
            path="/coordinator/coordinatorannotatorprojects"
            element={<CoordinatorAnnotatorProjects />}
          />
          <Route
            path="/coordinator/coordinatorprojects1"
            element={<CoordinatorClientProjects />}
          />
        </Route>
      </Route>
    </Route>
  )
);
