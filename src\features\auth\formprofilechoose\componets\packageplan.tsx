import { customAxios } from "@/utils/axio-interceptor";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FaCheck } from "react-icons/fa";
type Plan = {
  id: string;
  name: string;
  price: number;
  features: boolean[];
  description?: string;
  billingType?: string;
  isActive?: boolean;
};

type PlanTableProps = {
  plans: Plan[];
  featureLabels: string[];
};

type FeaturedType = {
  id: string;
  rule: string;
  packages: {
    packageId: string;
    available: boolean;
  }[];
};

const PlanTable: React.FC<PlanTableProps> = ({ plans }) => {
  const navigate = useNavigate();
  const [features, setFeatures] = useState<FeaturedType[]>([]);

  const getFeature = async () => {
    try {
      const response = await customAxios.get(
        "/v1/packages/get-package-features"
      );
      setFeatures(response.data.data.features);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getFeature();
  }, []);

  // Function to check if a feature is available for a specific plan
  const isFeatureAvailable = (planId: string, feature: FeaturedType) => {
    const packageFeature = feature.packages.find(
      (pkg) => pkg.packageId === planId
    );
    return packageFeature ? packageFeature.available : false;
  };

  return (
    <div className="w-full">
      <div className="overflow-x-auto CustomScroll">
        <table className="w-full border border-gray-200">
          <thead className="sticky top-0 z-10 bg-white">
            <tr>
              <th className="border border-gray-200 px-4 lg:px-6 xl:px-8 2xl:px-10 py-6 lg:py-8 xl:py-10 2xl:py-12 text-left min-w-[200px] lg:min-w-[230px] xl:min-w-[280px] 2xl:min-w-[350px] font-inter">
                <div className="flex flex-col gap-2 lg:gap-3 xl:gap-4 2xl:gap-5">
                  <div className="flex items-center gap-2 lg:gap-3 xl:gap-4 2xl:gap-5">
                    <h2 className="text-lg lg:text-xl xl:text-2xl 2xl:text-3xl font-semibold text-gray-800">
                      Compare plans
                    </h2>
                  </div>
                </div>
              </th>
              {plans.map((plan, idx) => (
                <th
                  key={idx}
                  className="border border-gray-200 px-4 lg:px-6 xl:px-8 2xl:px-10 py-2 lg:py-2 xl:py-2 2xl:py-12 text-center min-w-[180px] lg:min-w-[180px] xl:min-w-[200px] 2xl:min-w-[300px] font-inter"
                >
                  <h3 className="text-base lg:text-lg xl:text-xl 2xl:text-2xl font-semibold mb-1 lg:mb-1 xl:mb-1 2xl:mb-4">
                    {plan.name}
                  </h3>
                  <p className="text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl font-bold text-gray-800">
                    {plan.price}
                    <span className="text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium">
                      /month
                    </span>
                  </p>
                  <button
                    onClick={() =>
                      navigate(
                        `/auth/questionaire?packageId=${
                          plan.id
                        }&packageName=${encodeURIComponent(plan.name)}`
                      )
                    }
                    className="mt-2 lg:mt-3 xl:mt-4 2xl:mt-5 px-4 lg:px-6 xl:px-8 2xl:px-10 py-1.5 lg:py-2 xl:py-2.5 2xl:py-3 rounded-lg text-white font-medium text-xs lg:text-sm xl:text-base 2xl:text-lg bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] hover:opacity-90 transition-opacity"
                  >
                    Choose Plan
                  </button>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {features.map((feature) => (
              <tr key={feature.id} className="hover:bg-gray-50">
                <td className="border border-gray-200 px-4 lg:px-6 xl:px-8 2xl:px-10 py-3 lg:py-4 xl:py-5 2xl:py-6 text-xs lg:text-sm xl:text-base 2xl:text-lg">
                  {feature.rule}
                </td>
                {plans.map((plan) => (
                  <td
                    key={plan.id}
                    className="border border-gray-200 px-4 lg:px-6 xl:px-8 2xl:px-10 py-3 lg:py-4 xl:py-5 2xl:py-6 text-center  text-xs lg:text-sm xl:text-base 2xl:text-lg"
                  >
                    {isFeatureAvailable(plan.id, feature) ? (
                      <span className="text-green-500 text-center  text-base lg:text-lg xl:text-xl 2xl:text-2xl">
                        <FaCheck />
                      </span>
                    ) : (
                      <span className="text-red-500 text-base lg:text-lg xl:text-xl 2xl:text-2xl"></span>
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PlanTable;
