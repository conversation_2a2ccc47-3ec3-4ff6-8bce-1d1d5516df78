"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useInfiniteCoordinators, useInfiniteUsers } from "../api/query";
import { useUpdateAnnotatorCoordinatorMutation } from "../api/mutation";
import { useSelection } from "@/context/matchmaking.context";
import { ClientData } from "@/types/matchmaking.types";
import { toast } from "react-toastify";
import { useState, useMemo, useEffect } from "react";
import { useNavigate } from "react-router-dom";

const formatDate = (dateStr?: string | null) => {
  if (!dateStr) return "-";
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "-"; // Invalid date check

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = String(date.getFullYear()).slice(-2);
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting date:", error, dateStr);
    return "-";
  }
};

const formatShiftTiming = (
  availableFrom: string | null,
  availableTo: string | null
): string => {
  if (!availableFrom || !availableTo) {
    return "Not assigned";
  }

  const formatTime = (timeString: string) => {
    try {
      if (timeString.includes(":")) {
        const [hours, minutes] = timeString.split(":");
        const hour = parseInt(hours, 10);
        const ampm = hour >= 12 ? "PM" : "AM";
        const hour12 = hour % 12 || 12;
        return `${hour12}:${minutes} ${ampm}`;
      }

      const date = new Date(timeString);
      if (isNaN(date.getTime())) {
        return "Invalid time";
      }

      return date.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error("Error formatting time:", error);
      return "Invalid time";
    }
  };

  const fromFormatted = formatTime(availableFrom);
  const toFormatted = formatTime(availableTo);

  return `${fromFormatted} - ${toFormatted}`;
};

export const useAdminColumns = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<ClientData | null>(null);

  const { selectedAnnotators, setSelectedAnnotators, selectedCoordinators, setSelectedCoordinators } = useSelection();

  // Reset selections only when data is available
  const { data: userData } = useInfiniteUsers();
  const { data: coordinatorData } = useInfiniteCoordinators();
  useEffect(() => {
    if (!userData || !coordinatorData) return; // Wait for data to load

    const resetSelections = (rows: ClientData[]) => {
      rows.forEach((row) => {
        const clientId = row.id;
        const assignmentsAsClient = row.assignmentsAsClient || [];
        const assignedAnnotatorId = assignmentsAsClient[0]?.developerId;
        const assignedCoordinatorId = assignmentsAsClient[0]?.coordinatorId;
        if (clientId) {
          if (assignedAnnotatorId && selectedAnnotators[clientId] !== assignedAnnotatorId) {
            setSelectedAnnotators((prev) => ({ ...prev, [clientId]: assignedAnnotatorId }));
          } else if (!assignedAnnotatorId && selectedAnnotators[clientId]) {
            setSelectedAnnotators((prev) => ({ ...prev, [clientId]: "" }));
          }
          if (assignedCoordinatorId && selectedCoordinators[clientId] !== assignedCoordinatorId) {
            setSelectedCoordinators((prev) => ({ ...prev, [clientId]: assignedCoordinatorId }));
          } else if (!assignedCoordinatorId && selectedCoordinators[clientId]) {
            setSelectedCoordinators((prev) => ({ ...prev, [clientId]: "" }));
          }
        }
      });
    };

    // Assuming rows come from a prop or table data source
    const rows: ClientData[] = []; // Replace with actual row data (e.g., from table component)
    resetSelections(rows);
  }, [userData, coordinatorData, selectedAnnotators, selectedCoordinators, setSelectedAnnotators, setSelectedCoordinators]);

  const columns = useMemo<ColumnDef<ClientData>[]>(
    () => [
      {
        accessorKey: "client",
        header: ({ column }: { column: Column<ClientData, unknown> }) => (
          <div
            className="w-[120px] text-[14px] font-medium cursor-pointer"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Client
          </div>
        ),
        cell: ({ row }) => (
          <div
            className="pl-2 text-[14px] font-normal cursor-pointer hover:underline"
            onClick={() => {
              if (!row.original.subscriptionId) {
                toast.error("No subscription available for this client");
                return;
              }
              setSelectedClient(row.original);
              setIsModalOpen(true);
              navigate(`?subscriptionId=${row.original.subscriptionId}`);
            }}
          >
            {row.original.name}
          </div>
        ),
      },
      {
        accessorKey: "startDate",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="text-[14px] font-medium"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Started on
          </Button>
        ),
        cell: ({ row }) => {
          const startDate =
            row.original?.startDate ||
            (row.original?.Subscription &&
              (Array.isArray(row.original.Subscription)
                ? row.original.Subscription[0]?.startDate
                : row.original.Subscription?.startDate));
          return (
            <div className="pl-6 text-[14px] font-normal">
              {formatDate(startDate)}
            </div>
          );
        },
      },
      {
        accessorKey: "shifttiming",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="text-[14px] font-medium"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Shift Timing
          </Button>
        ),
        cell: ({ row }) => {
          if (!row.original) {
            return <div className="pl-4 text-[12px] font-normal">-</div>;
          }
          const clientData = row.original as ClientData;
          let availableFrom = null;
          let availableTo = null;
          if (
            clientData.clientPackageDetails &&
            clientData.clientPackageDetails.length > 0
          ) {
            availableFrom = clientData.clientPackageDetails[0].availableFrom;
            availableTo = clientData.clientPackageDetails[0].availableTo;
          } else {
            availableFrom = clientData.availableFrom;
            availableTo = clientData.availableTo;
          }
          return (
            <div className="pl-2 text-[14px] font-normal">
              {formatShiftTiming(availableFrom, availableTo)}
            </div>
          );
        },
      },
      {
        accessorKey: "enddate",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="text-[14px] font-medium"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            End Date
          </Button>
        ),
        cell: ({ row }) => {
          const endDate =
            row.original?.endDate ||
            (row.original?.Subscription &&
              (Array.isArray(row.original.Subscription)
                ? row.original.Subscription[0]?.endDate
                : row.original.Subscription?.endDate));
          return (
            <div className="pl-6 text-[14px] font-normal">
              {formatDate(endDate)}
            </div>
          );
        },
      },
      {
        accessorKey: "subscription",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="text-[14px] font-medium"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Subscription
          </Button>
        ),
        cell: ({ row }) => {
          const packageName =
            row.original?.packageName ||
            (row.original?.Subscription &&
              (Array.isArray(row.original.Subscription)
                ? row.original.Subscription[0]?.package?.name
                : row.original.Subscription?.package?.name));
          return (
            <div className="pl-8 text-[14px] font-normal">
              {packageName || "-"}
            </div>
          );
        },
      },
      {
        accessorKey: "annotator",
        header: () => <div className="text-[14px] font-medium">Annotator</div>,
        cell: ({ row }) => {
          if (!row.original) {
            console.error("Row original is undefined in annotator cell");
            return <div>Error: Invalid data</div>;
          }

          const assignmentsAsClient = row.original.assignmentsAsClient || [];
          const assignedAnnotatorId = assignmentsAsClient[0]?.developerId;
          const clientId = row.original.id;
          const subscription = Array.isArray(row.original.Subscription)
            ? row.original.Subscription[0]
            : row.original.Subscription;
          const packageId = subscription?.package?.id || row.original.packageId;
          const selectedAnnotator = clientId ? selectedAnnotators[clientId] || "" : "";
          const { data, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteUsers();
          const allAnnotators = data?.pages?.flatMap((page) => page.data) || [];

          let annotators = allAnnotators.filter((annotator) => !annotator.isAssigned);
          if (packageId) {
            annotators = annotators.filter(
              (annotator) => annotator.packageId === packageId || !annotator.packageId
            );
          }

          if (annotators.length === 0 && allAnnotators.length > 0) {
            annotators = allAnnotators.filter((annotator) => !annotator.isAssigned);
          }

          const selectedAnnotatorData = selectedAnnotator
            ? allAnnotators.find((a) => a.id === selectedAnnotator)
            : assignedAnnotatorId
            ? allAnnotators.find((a) => a.id === assignedAnnotatorId)
            : null;

          const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
            const target = e.currentTarget;
            const isBottom = target.scrollHeight - target.scrollTop === target.clientHeight;
            if (isBottom && hasNextPage && !isFetchingNextPage) {
              fetchNextPage();
            }
          };

          if (!clientId) {
            return <div className="text-sm text-red-500">Client ID is missing</div>;
          }

          const isAssigned = !!assignedAnnotatorId;

          return (
            <Select
              value={isAssigned ? assignedAnnotatorId : selectedAnnotator || ""}
              onValueChange={(value) => {
                const targetAnnotator = allAnnotators.find((a) => a.id === value);
                if (targetAnnotator && !targetAnnotator.isAssigned) {
                  setSelectedAnnotators((prev) => ({ ...prev, [clientId]: value }));
                } else {
                  toast.error("Cannot assign an already assigned annotator");
                }
              }}
              disabled={isAssigned}
            >
              <SelectTrigger className="border-gradient bg-[#F9EFEF]">
                <SelectValue placeholder="Select Annotator">
                  {selectedAnnotatorData?.name || "Select Annotator"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent onScroll={handleScroll} className="max-h-60 overflow-y-auto">
                {annotators.length > 0 ? (
                  annotators.map((annotator) => (
                    <SelectItem key={annotator.id} value={annotator.id}>
                      {annotator.name}
                      {packageId && annotator.packageId && annotator.packageId !== packageId && (
                        <span className="ml-2 text-xs text-yellow-500">(Plan mismatch)</span>
                      )}
                    </SelectItem>
                  ))
                ) : (
                  <div className="p-2 text-sm text-center text-muted-foreground">
                    No unassigned annotators available
                  </div>
                )}
                {isFetchingNextPage && (
                  <div className="p-2 text-sm text-center text-muted-foreground">
                    Loading more...
                  </div>
                )}
              </SelectContent>
            </Select>
          );
        },
      },
      {
        accessorKey: "coordinator",
        header: () => <div className="text-[14px] font-semibold">Coordinator</div>,
        cell: ({ row }) => {
          if (!row.original) {
            console.error("Row original is undefined in coordinator cell");
            return <div>Error: Invalid data</div>;
          }

          const assignmentsAsClient = row.original.assignmentsAsClient || [];
          const assignedCoordinatorId = assignmentsAsClient[0]?.coordinatorId;
          const clientId = row.original.id;

          if (!clientId) {
            console.error("Client ID is undefined in coordinator cell");
            return <div>Error: Invalid client ID</div>;
          }

          const selectedCoordinator = clientId ? selectedCoordinators[clientId] || "" : "";
          const { data, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteCoordinators();
          const coordinators = data?.pages?.flatMap((page) => page.data) || [];

          const selectedCoordinatorData = selectedCoordinator
            ? coordinators.find((c) => c.id === selectedCoordinator)
            : assignedCoordinatorId
            ? coordinators.find((c) => c.id === assignedCoordinatorId)
            : null;

          const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
            const target = e.currentTarget;
            const isBottom = target.scrollHeight - target.scrollTop === target.clientHeight;
            if (isBottom && hasNextPage && !isFetchingNextPage) {
              fetchNextPage();
            }
          };

          const isAssigned = !!assignedCoordinatorId;

          return (
            <Select
              value={isAssigned ? assignedCoordinatorId : selectedCoordinator || ""}
              onValueChange={(value) => {
                setSelectedCoordinators((prev) => ({ ...prev, [clientId]: value }));
              }}
              //disabled={isAssigned}
            >
              <SelectTrigger className="border-gradient bg-[#F9EFEF]">
                <SelectValue placeholder="Select Coordinator">
                  {selectedCoordinatorData?.name || "Select Coordinator"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent onScroll={handleScroll} className="max-h-60 overflow-y-auto">
                {coordinators.map((coordinator) => (
                  <SelectItem key={coordinator.id} value={coordinator.id}>
                    {coordinator.name}
                  </SelectItem>
                ))}
                {isFetchingNextPage && (
                  <div className="p-2 text-sm text-center text-muted-foreground">
                    Loading more...
                  </div>
                )}
              </SelectContent>
            </Select>
          );
        },
      },
      {
        id: "actions",
        header: () => <div className="text-[14px] font-medium">Actions</div>,
        cell: ({ row }) => {
          if (!row.original) {
            console.error("Row original is undefined");
            return <div>Error: Invalid data</div>;
          }
          const id = row.original.id;
          if (!id) {
            console.error("Client ID is undefined");
            return <div>Error: Invalid client ID</div>;
          }
          const { selectedAnnotators, selectedCoordinators } = useSelection();
          const { mutate: updateAnnotatorCoordinator } = useUpdateAnnotatorCoordinatorMutation();
          const { data: annotatorData } = useInfiniteUsers();
          if (!annotatorData) return <div>Loading...</div>; // Prevent rendering if data isn’t ready

          const assignmentsAsClient = row.original.assignmentsAsClient || [];
          const assignedAnnotatorId = assignmentsAsClient[0]?.developerId;
          const assignedCoordinatorId = assignmentsAsClient[0]?.coordinatorId;
          const currentAnnotator = selectedAnnotators[id] || "";
          const currentCoordinator = selectedCoordinators[id] || "";
          const isSaveDisabled = !currentAnnotator && !currentCoordinator && (assignedAnnotatorId || assignedCoordinatorId);

          const subscription = Array.isArray(row.original.Subscription)
            ? row.original.Subscription[0]
            : row.original.Subscription;
          const packageId = subscription?.package?.id || row.original.packageId || "";
          const allAnnotators = annotatorData?.pages?.flatMap((page) => page.data) || [];
          const selectedAnnotatorData = currentAnnotator
            ? allAnnotators.find((a) => a.id === currentAnnotator)
            : null;

          let packagesMatch = true;
          if (packageId && selectedAnnotatorData) {
            packagesMatch = !!selectedAnnotatorData.packageId && selectedAnnotatorData.packageId === packageId;
            // console.log("Package match result:", packagesMatch); // Uncomment for debugging if needed
          } else if (packageId && !selectedAnnotatorData) {
            packagesMatch = false;
            // console.log("Annotator has no package, assignment not allowed"); // Moved to debug only
          }

          const handleSave = () => {
            if (!row.original || !id) {
              toast.error("Invalid client data");
              return;
            }
            if (!packageId) {
              toast.error("Client has no package assigned");
              return;
            }
            if (currentAnnotator && !packagesMatch) {
              toast.error("Selected annotator's package does not match client's package");
              return;
            }
            const clientData = row.original as ClientData;
            let availableFrom = null;
            let availableTo = null;
            if (
              clientData.clientPackageDetails &&
              clientData.clientPackageDetails.length > 0
            ) {
              availableFrom = clientData.clientPackageDetails[0].availableFrom;
              availableTo = clientData.clientPackageDetails[0].availableTo;
            } else {
              availableFrom = clientData.availableFrom;
              availableTo = clientData.availableTo;
            }
            const formData = {
              clientId: id,
              developerId: currentAnnotator || assignedAnnotatorId,
              coordinatorId: currentCoordinator || assignedCoordinatorId,
              packageId,
              availableFrom: availableFrom || null,
              availableTo: availableTo || null,
            };
            console.log("Sending form data to API:", formData);
            if (!formData.developerId) {
              console.warn("Warning: developerId is empty in form data");
            }
            if (!formData.coordinatorId) {
              console.warn("Warning: coordinatorId is empty in form data");
            }
            updateAnnotatorCoordinator(formData);
          };

          return (
            <div className="flex flex-row gap-2">
              <Button
                variant="gradient"
                onClick={handleSave}
                className="px-8 text-[14px] font-normal"
               // disabled={isSaveDisabled}
                title={
                  isSaveDisabled
                    ? "Cannot save: An annotator or coordinator is already assigned"
                    : ""
                }
              >
                Save
              </Button>
            </div>
          );
        },
      },
    ],
    [selectedAnnotators, selectedCoordinators] // Add dependencies
  );

  return {
    columns,
    isModalOpen,
    selectedClient,
    openModal: (client: ClientData) => {
      setSelectedClient(client);
      setIsModalOpen(true);
      if (client.subscriptionId) {
        navigate(`?subscriptionId=${client.subscriptionId}`);
      } else {
        navigate("");
      }
    },
    closeModal: () => {
      setIsModalOpen(false);
      setSelectedClient(null);
      navigate("");
    },
  };
};