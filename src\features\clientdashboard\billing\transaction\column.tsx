"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { TransactionType } from "./transactiontype";
// import { FaDownload } from "react-icons/fa";
import { DownloadInvoiceButton } from "./DownloadInvoiceButton";

export const columns: ColumnDef<TransactionType>[] = [
  {
    accessorKey: "name",
    header: () => {
      return (
        <div
          className="w-[120px] text-[14px] font-medium cursor-pointer"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Package Name
        </div>
      );
    },
    cell: ({ row }) => (
        <div className="pl-2 text-[14px] font-normal">
        {row.getValue("name")}
      </div>
    ),
  },
  {
    accessorKey: "paymentId",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="w-[120px] text-[14px] font-medium cursor-pointer"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Payment Id
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className=" pl-4 text-[14px] font-normal">
        {row.getValue("paymentId")}
      </div>
    ),
  },
  {
    accessorKey: "amount",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="w-[120px] text-[14px] font-medium cursor-pointer"
       //   onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Amount
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className=" pl-7 text-[14px] font-normal">
        {row.getValue("amount")}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="w-[120px] text-[14px] font-medium cursor-pointer"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Payment Status
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className=" pl-9 text-[14px] font-normal">
        {row.getValue("status")}
      </div>
    ),
  },
  {
    accessorKey: "paymentMethod",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="w-[120px] text-[14px] font-medium cursor-pointer"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Payment Method
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className=" pl-8 text-[14px] font-normal">
        {row.getValue("paymentMethod")}
      </div>
    ),
  },
  {
    accessorKey: "next_billing_date",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="w-[120px] text-[14px] font-medium cursor-pointer"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Next Billing Date
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className=" pl-8 text-[14px] font-normal">
        {row.getValue("next_billing_date")}
      </div>
    ),
  },
 {
  id: "actions",
  cell: ({ row }) => (
    <DownloadInvoiceButton 
      paymentId={row.original.paymentId}
      customerName="client first" // Replace with actual data source
      customerEmail="<EMAIL>" // Replace with actual data source
      packageName={row.original.name}
      amount={row.original.amount}
      status={row.original.status}
      paymentMethod={row.original.paymentMethod}
      date={row.original.next_billing_date}
    />
  ),
}

];
