import { Route, Routes, Navigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { customAxios } from "@/utils/axio-interceptor";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import Attendacelog from "./annoatorattendancehistory/attendance.log.tsx";
import SettingFile from "@/features/settings/setting-file";
import FAQs from "@/features/settings/faqs";
import Subscription from "./billing/subscription";
import Dashboard from "./dashboard/dashboard-page.tsx";
import ClientNotification from "./clientnotification/index.tsx";
import { Loader2 } from "lucide-react";

export const ClientDashboardPage = () => {
  const [hasAnnotators, setHasAnnotators] = useState<boolean | null>(null);
  const userRole = useAppSelector((state) => state.auth.user?.role);

  useEffect(() => {
    const checkAnnotatorCount = async () => {
      try {
        if (userRole === "CLIENT") {
          const response = await customAxios.get(
            "/v1/dashboard/client-annotators"
          );
          setHasAnnotators(response.data?.count > 0);
        } else {
          // Coworkers don't need annotators
          setHasAnnotators(true);
        }
      } catch (error) {
        console.error("Error checking annotator count:", error);
        setHasAnnotators(false);
      }
    };

    checkAnnotatorCount();
  }, [userRole]);

  if (hasAnnotators === null) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin" />
      </div>
    );
  }

  if (userRole === "CLIENT" && !hasAnnotators) {
    return <Navigate to="/auth/login" replace />;
  }

  return (
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/attendance" element={<Attendacelog />} />
      <Route path="/billing/subscription" element={<Subscription />} />
      <Route path="/notification" element={<ClientNotification />} />
      <Route path="/settings" element={<SettingFile />} />
      <Route path="/faqs" element={<FAQs />} />

      {/* Add a catch-all route for invalid paths */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};
