import { redirectToDashboard } from "@/utils/navigaterole";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

// const getToken = (): string | null => {
//   const localToken = localStorage.getItem("token");
//   if (localToken) return localToken;

//   const cookieMatch = document.cookie.match(/(?:^|;\s*)token=([^;]*)/);
//   return cookieMatch ? cookieMatch[1] : null;
// };

// type AuthRedirectProps = {
//   children: React.ReactNode;
// };

// const AuthRedirect = ({ children }: AuthRedirectProps) => {
//   const navigate = useNavigate();
//   // const role = localStorage.getItem("role");

//   useEffect(() => {
//     const token = getToken();
//     if (token) {
//       // navigate("/dashboard");
//       // TODO - Uncomment the line below to redirect to the dashboard
//       redirectToDashboard("ADMIN", navigate);
//     }
//   }, [navigate]);

//   return <>{children}</>;
// };

// export default AuthRedirect;

const getToken = (): string | null => {
  const localToken = localStorage.getItem("token");
  if (localToken) return localToken;

  const cookieMatch = document.cookie.match(/(?:^|;\s*)token=([^;]*)/);
  return cookieMatch ? cookieMatch[1] : null;
};

type AuthRedirectProps = {
  children: React.ReactNode;
  skipForPaths?: string[]; // Add this prop
};

const AuthRedirect = ({ children, skipForPaths = [] }: AuthRedirectProps) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Skip redirection for specific paths (like password reset)
    if (skipForPaths.some((path) => location.pathname.includes(path))) {
      return;
    }

    const token = getToken();
    if (token) {
      // You'll need to get the user's role from somewhere (context, localStorage, etc.)
      const userRole = localStorage.getItem("userRole") || "CLIENT"; // Default or fetch properly
      redirectToDashboard(userRole, navigate);
    }
  }, [navigate, location, skipForPaths]);

  return <>{children}</>;
};

export default AuthRedirect;
