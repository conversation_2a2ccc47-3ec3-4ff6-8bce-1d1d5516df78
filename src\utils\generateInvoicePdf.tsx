import { jsPDF } from "jspdf";

interface InvoiceData {
  provider: string;
  paymentId: string;
  amount: string;
  status: string;
  paymentMethod: string;
  date: string;
  customerName: string;
  customerEmail: string;
  packageName: string;
  price: string;
}

export const generateInvoicePdf = (invoiceData: InvoiceData) => {
  const doc = new jsPDF();

  // Add logo or header
  doc.setFontSize(18);
  doc.setTextColor(40, 40, 40);
  doc.text("PAYMENT RECEIPT", 105, 20, { align: 'center' });

  // Add divider line
  doc.setDrawColor(200, 200, 200);
  doc.line(20, 25, 190, 25);

  // Provider and Payment Info
  doc.setFontSize(12);
  doc.text(`Provider: ${invoiceData.provider}`, 20, 35);
  doc.text(`Payment ID: ${invoiceData.paymentId}`, 20, 45);
  doc.text(`Date: ${invoiceData.date}`, 20, 55);
  doc.text(`Status: ${invoiceData.status}`, 20, 65);
  doc.text(`Payment Method: ${invoiceData.paymentMethod}`, 20, 75);

  // Amount on right side
  doc.setFontSize(16);
  doc.text(`Amount: ${invoiceData.amount}`, 140, 35);

  // Customer Information
  doc.setFontSize(12);
  doc.text("Customer Information:", 20, 95);
  doc.text(`Name: ${invoiceData.customerName}`, 20, 105);
  doc.text(`Email: ${invoiceData.customerEmail}`, 20, 115);

  // Package Information
  doc.text("Package Information:", 20, 135);
  doc.text(`Package: ${invoiceData.packageName}`, 20, 145);
  doc.text(`Price: ${invoiceData.price}`, 20, 155);

  // Footer
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text("This is a system-generated receipt.", 105, 180, { align: 'center' });
  doc.text("For any queries, please contact support.", 105, 185, { align: 'center' });
  doc.text(`Generated on: ${new Date().toISOString()}`, 105, 190, { align: 'center' });

  return doc;
};