import img1 from "@/assets/darklogo.png";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormControl,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import FileUpload from "./common/file-uplaod";
import { useLocation, useNavigate } from "react-router-dom";
import { PostBankTransfer } from "./banktransfer-api/banktransfer-api";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Form Schema
const formSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
  AccountName: z
    .string()
    .min(3, "Holder name is required")
    .max(20, "Account name  is maximum 20 words limit")
    .regex(/^[A-Za-z\s]+$/, "Only letters and spaces are allowed"),
  amount: z
    .string()
    .min(1, "Ammount is required")
    .max(20, "Amount is maximum number")
    .regex(/^\d+$/, "Only numbers are allowed"),
  transactionDate: z
    .string()
    .min(1, "Transaction date is required")
    .regex(
      /^\d{4}-\d{2}-\d{2}$/,
      "Date must be in YYYY-MM-DD format"
    )
    .refine(
      (val) => !isNaN(Date.parse(val)),
      "Invalid date format"
    ),
  transferbankaccount: z.string().min(1, "transferbankaccount is required"),
  accountNumber: z.string().optional(), // Make accountNumber optional
  document: z
    .instanceof(File)
    .refine((file) => file.size > 0, "Please upload a file")
    .refine(
      (file) => file.size <= 5 * 1024 * 1024,
      "File size must be less than 5MB"
    )
    .refine(
      (file) =>
        ["image/jpeg", "image/png", "image/jpg", "application/pdf"].includes(
          file.type
        ),
      "Only JPEG, PNG, JPG, or PDF files are allowed"
    ),
});

const BankTransfer = () => {
  const { state } = useLocation();
  const navigate = useNavigate();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      transactionId: "",
      transactionDate: "",
      accountNumber: "",
      AccountName: "",
      transferbankaccount: "",
      amount: "",
      document: undefined,
    },
    mode: "onBlur",
  });

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (!state) {
      toast.error("Missing required data. Please start the process again.", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    try {
      const submissionData = {
        ...state.formData, // Contains: availableFrom, availableTo, timezone, etc.
        ...state.billingData, // Contains: city, zipcode, country, state, street
        amount: data.amount,
        transactionId: data.transactionId,
        bankHolderName: data.AccountName,
        accountNumber: data.accountNumber || null,
        bankName: data.transferbankaccount,
        screenshot: data.document,
        transactionDate: data.transactionDate,
        transferedAccNo: data.transferbankaccount,
        currency: "USD", // You can make this dynamic if needed
      };

      const response = await PostBankTransfer(submissionData);
      console.log("banktrafer data", response)

      if (response.status === 1) {
        toast.success(response.message);
        navigate("/dashboard/subscription-success"); // Or your success route
      } else {
        toast.error("Submission failed. Please try again.");
      }
    } catch (error) {
      console.error("Bank transfer submission error:");
      console.error("Bank transfer submission error:", error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <img src={img1} className="w-[155px] h-[32px]" alt="Company Logo" />
          <Button variant={"gradient"} className="px-6 py-3">
            <a
              href="https://dashboard.skydo.com/accounts/macgence?location=usa&currency=native"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white hover:no-underline"
            >
              Account Details
            </a>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md p-8">
          <h2 className="text-2xl font-bold mb-8 text-center text-gray-800">
            Bank Transfer Details
          </h2>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* First Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Transaction ID */}
                <FormField
                  control={form.control}
                  name="transactionId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Transaction ID
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Transaction ID"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur} // Ensure onBlur is properly handled
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />

                {/* Transaction Date */}
                <FormField
                  control={form.control}
                  name="transactionDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Transaction Date
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            type="date"
                            placeholder="Select Transaction Date"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) => field.onChange(e.target.value)}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Account Number - Now Optional */}
                <FormField
                  control={form.control}
                  name="accountNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Account Number (Optional)
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Account Number (Optional)"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />

                {/* IFSC Code */}
                <FormField
                  control={form.control}
                  name="AccountName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Account Name
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Account Name"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />
              </div>

              {/* third row  */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Bank tranfer accountName */}
                <FormField
                  control={form.control}
                  name="transferbankaccount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Transfer Bank Account
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Transfer Bank Account"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />

                {/* tranfer amount */}
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Amount
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Transfer Amount"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />
              </div>

              {/* Document Upload */}
              <FormField
                control={form.control}
                name="document"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700 font-medium">
                      Upload Document (Proof of Transfer)
                      <span className="text-gray-500 text-sm ml-2">
                        (JPEG, PNG, JPG, or PDF, max 5MB)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <div
                        className="border-gradient flex justify-center items-center rounded-lg p-[2px]"
                        tabIndex={-1} // Make the div focusable
                        onBlur={field.onBlur} // Handle blur on the wrapper div
                      >
                        <FileUpload
                          onChange={field.onChange}
                          value={field.value}
                          accept=".jpg,.jpeg,.png,.pdf"
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="text-red-500 text-sm" />
                  </FormItem>
                )}
              />

              {/* Submit Button */}
              <div className="flex justify-center pt-4">
                <Button
                  variant={"gradient"}
                  type="submit"
                  className="px-12 py-4 text-lg font-medium"
                >
                  Submit
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </main>
    </div>
  );
};

export default BankTransfer;