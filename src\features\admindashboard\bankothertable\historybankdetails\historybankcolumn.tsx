"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { BankDetailsType } from "../bankouthetable/bankdetails.type";
import { ImageModal } from "../banktransferapi/ImageModal";
import { useState } from "react";

const formatDate = (dateStr?: string | null) => {
  if (!dateStr) return "-";
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "-"; // Invalid date check

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = String(date.getFullYear()).slice(-2);
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting date:", error, dateStr);
    return "-";
  }
};

// Format currency with Indian Rupee symbol
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    maximumFractionDigits: 0,
  }).format(amount);
};

// useAdminColumns hook definition
export const historyuseAdminColumns = (refreshData: () => void): ColumnDef<BankDetailsType>[] => {
  return [
    {
      // user.name
      accessorKey: "name",
      header: () => (
        <div
          className="w-[120px] text-[14px]font-medium cursor-pointer"
        // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Client
        </div>
      ),
      cell: ({ row }) => (
        <div className="pl-2 text-[14px] font-normal">
          {row.original.name}
        </div>
      ),
    },
    {
      accessorKey: "transactionId",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Transaction ID
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-2 text-[14px] font-normal">{row.original.transactionId}</div>
      ),
    },
    {
      accessorKey: "transactionDate",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px]  font-medium"
        //   onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Transaction Date
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {formatDate(row.original.transactionDate)}
        </div>
      ),
    },
    {
      accessorKey: "transferedAccNo",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //   onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          TransferAccount No.
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {row.original.transferedAccNo}
        </div>
      ),
    },
    {
      accessorKey: "bankHolderName",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          BankHolder Name
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {row.original.bankHolderName}
        </div>
      ),
    },
    {
      accessorKey: "amount",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Amount
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {formatCurrency(row.original.amount)}
        </div>
      ),
    },
    // {
    //   accessorKey: "accountNumber",
    //   header: () => (
    //     <Button
    //       variant="ghost"
    //       className="text-[14px] font-medium"
    //     //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    //     >
    //       Account Number
    //     </Button>
    //   ),
    //   cell: ({ row }) => (
    //     <div className="pl-2 text-[14px] font-normal">
    //     {row.original.accountNumber}
    //     </div>
    //   ),
    // },
    {
      accessorKey: "accountNumber",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Account Number
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {row.original.accountNumber}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        >
          Status
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.original.status.toLowerCase();
        let statusClass = "";

        // Set color based on payment status
        if (status === "verified" || status === "paid") {
          statusClass = "text-white bg-green-600 py-2 text-center rounded-lg";
        } else if (status === "rejected" || status === "failed") {
          statusClass = "text-white bg-red-600 py-2 text-center rounded-lg";
        } else if (status === "pending") {
          statusClass = "text-white bg-yellow-600 py-2 text-center rounded-lg";
        } else {
          statusClass = "text-gray-600";
        }

        return (
          <div className={`text-[14px] font-normal ${statusClass}`}>
            {row.original.status}
          </div>
        );
      },
    },
     {
      accessorKey: "screenshotUrl",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Proof Image
        </Button>
      ),
      cell: ({ row }) => {
        const [isModalOpen, setIsModalOpen] = useState(false);
        const imageUrl = row.original.screenshotUrl;

        return (
          <div className=" flex flex-row justify-center items-center text-[14px] font-normal">
            {imageUrl ? (
              <>
                <img
                  src={imageUrl}
                  alt="Proof Thumbnail"
                  className="w-10 h-10 object-cover cursor-pointer rounded"
                  onClick={() => setIsModalOpen(true)}
                />
                <ImageModal
                  isOpen={isModalOpen}
                  imageUrl={imageUrl}
                  onClose={() => setIsModalOpen(false)}
                />
              </>
            ) : (
              "-"
            )}
          </div>
        );
      },
    },


  ];
};
